defmodule Drops.TestRepo.Migrations.CreateBasicTypesTable do
  use Ecto.Migration

  def change do
    # Basic types table with core Ecto types
    create table(:basic_types) do
      add :string_field, :string
      add :integer_field, :integer
      add :float_field, :float
      add :boolean_field, :boolean
      add :binary_field, :binary
      add :bitstring_field, :text  # SQLite doesn't have bitstring, use text
      add :decimal_field, :decimal
      add :date_field, :date
      add :time_field, :time
      add :naive_datetime_field, :naive_datetime
      add :utc_datetime_field, :utc_datetime
      add :map_field, :map

      timestamps()
    end
  end
end
