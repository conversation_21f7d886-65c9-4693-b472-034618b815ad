defmodule ExampleSetup do
  @moduledoc """
  Helper functions for setting up database state in examples.

  This module provides utilities for examples to set up database tables
  and seed data as needed.
  """

  @doc """
  Sets up the database with the necessary tables for the given schemas.
  Call this at the beginning of your example if you need database access.
  """
  def setup_database(_schemas \\ []) do
    repo_config = Application.get_env(:drops, Drops.Repos.Sqlite, [])

    # Ensure the database exists and run migrations
    ensure_database_setup()

    pid =
      if repo_config[:pool] == Ecto.Adapters.SQL.Sandbox do
        Ecto.Adapters.SQL.Sandbox.start_owner!(Drops.Repos.Sqlite, shared: false)
      else
        :no_sandbox
      end

    pid
  end

  defp ensure_database_setup do
    # Create the database if it doesn't exist
    case Ecto.Adapters.SQLite3.storage_up(Drops.Repos.Sqlite.config()) do
      :ok -> :ok
      {:error, :already_up} -> :ok
      {:error, reason} -> raise "Failed to create database: #{inspect(reason)}"
    end

    # Run migrations to ensure tables exist
    migrations_path = Application.app_dir(:drops, "priv/repo/dev/sqlite/migrations")

    if File.exists?(migrations_path) do
      Ecto.Migrator.run(Drops.Repos.Sqlite, migrations_path, :up, all: true)
    end
  end

  def cleanup_database(:no_sandbox), do: :ok

  def cleanup_database(pid) do
    Ecto.Adapters.SQL.Sandbox.stop_owner(pid)
  end
end
