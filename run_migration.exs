IO.puts("Running missing migrations...")

# Start the repo
{:ok, _} = Application.ensure_all_started(:drops)

# Run the missing migrations
migration_dir = "priv/repo/test/postgres/migrations"
repo = Drops.Repos.Postgres

IO.puts("Migration directory: #{migration_dir}")
IO.puts("Repo: #{repo}")

# Check current migration status
{:ok, result} = Ecto.Adapters.SQL.query(repo, "SELECT version FROM schema_migrations ORDER BY version", [])
IO.puts("Current migrations:")
Enum.each(result.rows, fn [version] -> IO.puts("  #{version}") end)

# Try to run migrations
try do
  result = Ecto.Migrator.run(repo, migration_dir, :up, all: true, log: true)
  IO.puts("Migration result: #{inspect(result)}")
rescue
  error ->
    IO.puts("Migration error: #{inspect(error)}")
end

# Check final migration status
{:ok, result} = Ecto.Adapters.SQL.query(repo, "SELECT version FROM schema_migrations ORDER BY version", [])
IO.puts("Final migrations:")
Enum.each(result.rows, fn [version] -> IO.puts("  #{version}") end)
