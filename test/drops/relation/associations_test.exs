defmodule Drops.Relations.AssociationsTest do
  use Drops.RelationCase, async: false

  describe "associations" do
    @tag relations: [:user_groups]

    relation(:users) do
      associations do
        many_to_many(:groups, Test.Relations.Groups.Struct, join_through: "user_groups")
      end
    end

    relation(:groups) do
      associations do
        many_to_many(:users, Test.Relations.Users.Struct, join_through: "user_groups")
      end
    end

    test "sets up regular Ecto associations", %{users: users} do
      associations = users.associations()
      assert length(associations) > 0
      assert :groups in associations

      assert groups = users.association(:groups)

      assert groups.__struct__ == Ecto.Association.ManyToMany
    end
  end
end
