import Config

# Ensure log directory exists
log_dir = Path.join(File.cwd!(), "log")

# Configure logger to write Ecto logs to file
config :logger,
  backends: [
    {LoggerFileBackend, :ecto_test},
    {LoggerFileBackend, :drops_test}
  ]

# Configure Ecto file logger
config :logger, :ecto_test,
  path: Path.join(log_dir, "ecto_test.log"),
  level: :info,
  format: "$time $metadata[$level] $message\n",
  metadata: [:query_time, :decode_time, :queue_time, :connection_time]

# Configure Drops file logger
config :logger, :drops_test,
  path: Path.join(log_dir, "test.log"),
  level: :info,
  format: "$time $metadata[$level] $message\n"

# Configure the original TestRepo (SQLite) for backward compatibility
config :drops, Drops.TestRepo,
  adapter: Ecto.Adapters.SQLite3,
  database: "tmp/data_case_sqlite.db",
  pool: Ecto.Adapters.SQL.Sandbox,
  pool_size: 10,
  queue_target: 5000,
  queue_interval: 1000,
  log: :info,
  loggers: [{Ecto.LogEntry, :log, [:ecto_test]}],
  priv: "priv/repo/test/sqlite"

# Configure the SQLite test repository
config :drops, Drops.Repos.Sqlite,
  adapter: Ecto.Adapters.SQLite3,
  database: "tmp/test_sqlite.db",
  pool: Ecto.Adapters.SQL.Sandbox,
  pool_size: 10,
  queue_target: 5000,
  queue_interval: 1000,
  log: :info,
  loggers: [{Ecto.LogEntry, :log, [:ecto_test]}],
  priv: "priv/repo/test/sqlite"

# Configure the PostgreSQL test repository
config :drops, Drops.Repos.Postgres,
  adapter: Ecto.Adapters.Postgres,
  username: "postgres",
  password: "postgres",
  hostname: "postgres",
  database: "drops_test",
  pool: Ecto.Adapters.SQL.Sandbox,
  pool_size: 10,
  queue_target: 5000,
  queue_interval: 1000,
  log: :info,
  loggers: [{Ecto.LogEntry, :log, [:ecto_test]}],
  priv: "priv/repo/test/postgres"

# Configure Ecto repos
config :drops, :ecto_repos, [Drops.TestRepo, Drops.Repos.Sqlite, Drops.Repos.Postgres]

# Configure schema cache for test environment
config :drops, :schema_cache,
  enabled: true,
  max_entries: 100,
  cleanup_interval: :never
