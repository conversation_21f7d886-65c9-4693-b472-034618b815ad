import Config

# Configure the original TestRepo (SQLite) for backward compatibility in dev environment
config :drops, Drops.TestRepo,
  adapter: Ecto.Adapters.SQLite3,
  database: "tmp/dev_test_repo.db",
  pool_size: 1,
  queue_target: 5000,
  queue_interval: 1000,
  log: :info,
  priv: "priv/repo/dev/sqlite"

# Configure the SQLite repository for examples in dev environment
config :drops, Drops.Repos.Sqlite,
  adapter: Ecto.Adapters.SQLite3,
  database: "tmp/dev_sqlite.db",
  pool_size: 1,
  queue_target: 5000,
  queue_interval: 1000,
  log: :info,
  priv: "priv/repo/dev/sqlite"

# Configure the PostgreSQL repository for examples in dev environment
config :drops, Drops.Repos.Postgres,
  adapter: Ecto.Adapters.Postgres,
  username: "postgres",
  password: "postgres",
  hostname: "postgres",
  database: "drops_dev",
  pool_size: 2,
  queue_target: 5000,
  queue_interval: 1000,
  log: :info,
  priv: "priv/repo/dev/postgres"

# Configure Ecto repos
config :drops, :ecto_repos, [Drops.TestRepo, Drops.Repos.Sqlite, Drops.Repos.Postgres]
