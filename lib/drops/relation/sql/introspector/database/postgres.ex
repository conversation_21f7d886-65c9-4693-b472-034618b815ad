defmodule Drops.Relation.SQL.Introspector.Database.Postgres do
  @moduledoc """
  PostgreSQL implementation of the Database behavior for schema introspection.

  This module provides PostgreSQL-specific implementations for database introspection
  operations using PostgreSQL's system catalogs and information schema.

  ## Features

  - Index introspection via system catalogs
  - Column metadata extraction via information schema
  - PostgreSQL type to Ecto type conversion
  - Support for various PostgreSQL index types (btree, hash, gin, gist, brin)
  """

  @behaviour Drops.Relation.SQL.Introspector.Database

  alias Drops.Relation.Schema.{Index, Indices}

  @impl true
  def get_table_indices(repo, table_name) do
    query = """
    SELECT
        i.relname as index_name,
        array_agg(a.attname ORDER BY array_position(ix.indkey, a.attnum)) as column_names,
        ix.indisunique as is_unique,
        am.amname as index_type
    FROM pg_class t
    JOIN pg_index ix ON t.oid = ix.indrelid
    JOIN pg_class i ON i.oid = ix.indexrelid
    JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
    JOIN pg_am am ON i.relam = am.oid
    WHERE t.relname = $1
      AND NOT ix.indisprimary  -- Exclude primary key indices
    GROUP BY i.relname, ix.indisunique, am.amname
    ORDER BY i.relname
    """

    case repo.query(query, [table_name]) do
      {:ok, %{rows: rows}} ->
        indices =
          for [index_name, column_names, is_unique, index_type] <- rows do
            field_names = Enum.map(column_names, &String.to_atom/1)
            type = index_type_to_atom(index_type)

            Index.from_names(index_name, field_names, is_unique, type)
          end

        {:ok, Indices.new(indices)}

      {:error, error} ->
        {:error, error}
    end
  end

  @impl true
  def introspect_table_columns(repo, table_name) do
    query = """
    SELECT
        c.column_name,
        c.data_type,
        c.is_nullable,
        c.column_default,
        CASE
            WHEN tc.constraint_type = 'PRIMARY KEY' THEN true
            ELSE false
        END as is_primary_key
    FROM information_schema.columns c
    LEFT JOIN information_schema.key_column_usage kcu
        ON c.table_name = kcu.table_name
        AND c.column_name = kcu.column_name
        AND c.table_schema = kcu.table_schema
    LEFT JOIN information_schema.table_constraints tc
        ON kcu.constraint_name = tc.constraint_name
        AND kcu.table_schema = tc.table_schema
        AND tc.constraint_type = 'PRIMARY KEY'
    WHERE c.table_name = $1
        AND c.table_schema = 'public'
    ORDER BY c.ordinal_position
    """

    case repo.query(query, [table_name]) do
      {:ok, %{rows: rows}} ->
        Enum.map(rows, fn [
                            column_name,
                            data_type,
                            is_nullable,
                            _column_default,
                            is_primary_key
                          ] ->
          %{
            name: column_name,
            type: data_type,
            not_null: is_nullable == "NO",
            primary_key: is_primary_key
          }
        end)

      {:error, error} ->
        raise "Failed to introspect table #{table_name}: #{inspect(error)}"
    end
  end

  @impl true
  def db_type_to_ecto_type(postgres_type, field_name) do
    # Handle array types first (since we can't use String.ends_with? in guards)
    if String.ends_with?(postgres_type, "[]") do
      base_type = String.trim_trailing(postgres_type, "[]")
      {:array, db_type_to_ecto_type(base_type, field_name)}
    else
      downcased = String.downcase(postgres_type)
      convert_base_type(downcased, field_name)
    end
  end

  # Convert non-array PostgreSQL types to Ecto types
  defp convert_base_type(postgres_type, _field_name) do
    case postgres_type do
      # Integer types and aliases
      "integer" ->
        :integer

      "int" ->
        :integer

      "int4" ->
        :integer

      "bigint" ->
        :integer

      "int8" ->
        :integer

      "smallint" ->
        :integer

      "int2" ->
        :integer

      # Serial types
      "serial" ->
        :id

      "bigserial" ->
        :id

      # Floating point types and aliases
      "real" ->
        :float

      "float4" ->
        :float

      "double precision" ->
        :float

      "float8" ->
        :float

      # Decimal types
      "numeric" ->
        :decimal

      "decimal" ->
        :decimal

      "money" ->
        :decimal

      # String types
      "text" ->
        :string

      "character varying" ->
        :string

      "varchar" ->
        :string

      "char" ->
        :string

      "character" ->
        :string

      # PostgreSQL internal name type
      "name" ->
        :string

      # Boolean type
      "boolean" ->
        :boolean

      # Binary types
      "bytea" ->
        :binary

      # Date/time types
      "date" ->
        :date

      "time without time zone" ->
        :time

      "time with time zone" ->
        :time

      "timetz" ->
        :time

      "timestamp without time zone" ->
        :naive_datetime

      "timestamp" ->
        :naive_datetime

      "timestamp with time zone" ->
        :utc_datetime

      "timestamptz" ->
        :utc_datetime

      # JSON types
      "json" ->
        :map

      "jsonb" ->
        :map

      # UUID type
      "uuid" ->
        :binary_id

      # XML type
      "xml" ->
        :string

      # Network types (mapped to string for now, could be custom types later)
      "inet" ->
        :string

      "cidr" ->
        :string

      "macaddr" ->
        :string

      # Geometric types (mapped to string for now, could be custom types later)
      "point" ->
        :string

      "line" ->
        :string

      "lseg" ->
        :string

      "box" ->
        :string

      "path" ->
        :string

      "polygon" ->
        :string

      "circle" ->
        :string

      # Interval type (could be custom type later)
      "interval" ->
        :string

      # Handle complex types that need pattern matching
      type ->
        cond do
          String.ends_with?(type, "range") -> :string
          String.starts_with?(type, "character varying(") -> :string
          String.starts_with?(type, "varchar(") -> :string
          String.starts_with?(type, "character(") -> :string
          String.starts_with?(type, "char(") -> :string
          String.starts_with?(type, "numeric(") -> :decimal
          String.starts_with?(type, "decimal(") -> :decimal
          true -> :string
        end
    end
  end

  @impl true
  def index_type_to_atom(type_string) do
    case String.downcase(type_string) do
      "btree" -> :btree
      "hash" -> :hash
      "gin" -> :gin
      "gist" -> :gist
      "brin" -> :brin
      "spgist" -> :spgist
      _ -> nil
    end
  end
end
