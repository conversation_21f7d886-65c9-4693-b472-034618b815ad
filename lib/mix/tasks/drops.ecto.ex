defmodule Mix.Tasks.Drops.Ecto do
  @moduledoc """
  Wrapper for Ecto tasks with adapter selection support.

  This task allows running Ecto commands with a specific adapter.
  Defaults to SQLite if no adapter is specified.

  ## Usage

      mix drops.ecto create --adapter sqlite
      mix drops.ecto migrate --adapter postgres
      mix drops.ecto setup --adapter sqlite
      mix drops.ecto reset --adapter postgres --force

  ## Available adapters

  - sqlite (default)
  - postgres

  ## Available commands

  - create
  - drop
  - migrate
  - rollback
  - setup
  - reset
  - ecto.gen.migration

  All commands support the same options as their corresponding ecto.* tasks,
  plus the --adapter option for selecting the database adapter.

  ## Examples

      # Create database with SQLite (default)
      mix drops.ecto create

      # Migrate with PostgreSQL
      mix drops.ecto migrate --adapter postgres

      # Reset database with force flag
      mix drops.ecto reset --adapter postgres --force

      # Generate migration
      mix drops.ecto ecto.gen.migration add_users_table --adapter sqlite

  """

  use Mix.Task

  @shortdoc "Run Ecto commands with adapter selection"

  @impl Mix.Task
  def run(args) do
    {opts, remaining_args} = parse_args(args)
    adapter = opts[:adapter] || :sqlite

    # Parse command and arguments
    {command, additional_args} = parse_command_and_args(remaining_args)

    # Set the repo based on adapter
    repo_module =
      case adapter do
        :sqlite -> Drops.Repos.Sqlite
        :postgres -> Drops.Repos.Postgres
        _ -> Mix.raise("Unknown adapter: #{adapter}. Available: sqlite, postgres")
      end

    repo_string = to_string(repo_module)

    # Run the setup task first to ensure environment is ready
    Mix.Task.run("drops.dev.setup")

    # Build final args for ecto tasks, always including --repo
    ecto_args = ["--repo", repo_string] ++ additional_args

    case command do
      "create" ->
        Mix.Task.run("ecto.create", ecto_args)

      "drop" ->
        Mix.Task.run("ecto.drop", ecto_args)

      "migrate" ->
        Mix.Task.run("ecto.migrate", ecto_args)

      "rollback" ->
        Mix.Task.run("ecto.rollback", ecto_args)

      "setup" ->
        # Setup: create + migrate
        Mix.Task.run("ecto.create", ["--repo", repo_string])
        Mix.Task.run("ecto.migrate", ["--repo", repo_string])

      "reset" ->
        # Reset: safely drop + create + migrate
        safe_reset(repo_module, repo_string, additional_args)

      "gen.migration" ->
        if length(additional_args) < 1 do
          Mix.raise("ecto.gen.migration requires a migration name")
        end

        Mix.Task.run("ecto.gen.migration", ecto_args)

      _ ->
        Mix.raise(
          "Unknown command: #{command}. Available: create, drop, migrate, rollback, setup, reset, ecto.gen.migration"
        )
    end
  end

  # Safe reset that handles database connections properly
  defp safe_reset(repo_module, repo_string, additional_args) do
    # Use appropriate flags for PostgreSQL to handle active connections
    force_args =
      case repo_module.__adapter__() do
        Ecto.Adapters.Postgres ->
          # Add --force-drop flag if not already present for PostgreSQL
          # This forces the database to be dropped even with active connections
          args_with_force_drop =
            if "--force-drop" in additional_args do
              additional_args
            else
              ["--force-drop"] ++ additional_args
            end

          # Also add --force to avoid confirmation prompts
          if "--force" in args_with_force_drop or "-f" in args_with_force_drop do
            args_with_force_drop
          else
            ["--force"] ++ args_with_force_drop
          end

        _ ->
          # For SQLite and other adapters, just add --force to avoid prompts
          if "--force" in additional_args or "-f" in additional_args do
            additional_args
          else
            ["--force"] ++ additional_args
          end
      end

    # Proceed with the reset using appropriate flags
    Mix.Task.run("ecto.drop", ["--repo", repo_string] ++ force_args)
    Mix.Task.run("ecto.create", ["--repo", repo_string])

    # For PostgreSQL, restart the repo to clear stale connections
    if repo_module.__adapter__() == Ecto.Adapters.Postgres do
      restart_repo(repo_module)
    end

    Mix.Task.run("ecto.migrate", ["--repo", repo_string])
  end

  # Restart a repo to clear stale connections
  defp restart_repo(repo_module) do
    try do
      # Stop the repo if it's running
      if Process.whereis(repo_module) do
        Supervisor.stop(repo_module, :normal, 5000)
      end

      # Wait a moment for cleanup
      Process.sleep(100)

      # Restart the repo
      repo_module.start_link()
    rescue
      error ->
        Mix.shell().info(
          "Warning: Could not restart repo #{repo_module}: #{inspect(error)}"
        )
    end
  end

  # Parse command and additional arguments, handling multi-word commands like "ecto.gen.migration"
  defp parse_command_and_args(remaining_args) do
    case remaining_args do
      [] ->
        {"setup", []}

      ["ecto.gen.migration" | rest] ->
        {"ecto.gen.migration", rest}

      [command | rest] ->
        {command, rest}
    end
  end

  defp parse_args(args) do
    {opts, remaining, _} =
      OptionParser.parse(args,
        switches: [adapter: :string],
        aliases: [a: :adapter]
      )

    # Convert adapter string to atom
    opts =
      if opts[:adapter] do
        Keyword.put(opts, :adapter, String.to_atom(opts[:adapter]))
      else
        opts
      end

    {opts, remaining}
  end
end
