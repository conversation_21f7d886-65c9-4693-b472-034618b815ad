#!/usr/bin/env elixir

# Debug script to investigate PostgreSQL schema inference issues

Mix.install([
  {:ecto, "~> 3.10"},
  {:ecto_sql, "~> 3.10"},
  {:postgrex, "~> 0.17"}
])

# Set up a simple test to debug the issue
defmodule DebugPostgresInference do
  def run do
    # Start the application
    Application.ensure_all_started(:ecto)
    Application.ensure_all_started(:postgrex)
    
    # Configure a test repo
    defmodule TestRepo do
      use Ecto.Repo,
        otp_app: :debug,
        adapter: Ecto.Adapters.Postgres
    end
    
    # Start the repo
    {:ok, _} = TestRepo.start_link([
      hostname: "localhost",
      username: "postgres", 
      password: "postgres",
      database: "drops_test",
      pool: Ecto.Adapters.SQL.Sandbox
    ])
    
    # Test basic connection
    IO.puts("Testing basic PostgreSQL connection...")
    result = Ecto.Adapters.SQL.query(TestRepo, "SELECT 1 as test", [])
    IO.inspect(result, label: "Basic query result")
    
    # Test table introspection
    IO.puts("\nTesting table introspection...")
    
    # Create a simple test table
    Ecto.Adapters.SQL.query!(TestRepo, """
    DROP TABLE IF EXISTS debug_test_table
    """)
    
    Ecto.Adapters.SQL.query!(TestRepo, """
    CREATE TABLE debug_test_table (
      id SERIAL PRIMARY KEY,
      name VARCHAR(255) NOT NULL,
      age INTEGER,
      active BOOLEAN DEFAULT true,
      created_at TIMESTAMP DEFAULT NOW()
    )
    """)
    
    # Test information_schema query
    query = """
    SELECT
        c.column_name,
        c.data_type,
        c.is_nullable,
        c.column_default,
        CASE
            WHEN tc.constraint_type = 'PRIMARY KEY' THEN true
            ELSE false
        END as is_primary_key
    FROM information_schema.columns c
    LEFT JOIN information_schema.key_column_usage kcu
        ON c.table_name = kcu.table_name
        AND c.column_name = kcu.column_name
        AND c.table_schema = kcu.table_schema
    LEFT JOIN information_schema.table_constraints tc
        ON kcu.constraint_name = tc.constraint_name
        AND kcu.table_schema = tc.table_schema
        AND tc.constraint_type = 'PRIMARY KEY'
    WHERE c.table_name = $1
        AND c.table_schema = 'public'
    ORDER BY c.ordinal_position
    """
    
    result = Ecto.Adapters.SQL.query(TestRepo, query, ["debug_test_table"])
    IO.inspect(result, label: "Column introspection result")
    
    # Clean up
    Ecto.Adapters.SQL.query!(TestRepo, "DROP TABLE debug_test_table")
    
    IO.puts("\nDebug complete!")
  end
end

DebugPostgresInference.run()
